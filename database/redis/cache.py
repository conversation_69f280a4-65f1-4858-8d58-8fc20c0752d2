import redis
import json
from typing import Optional, Dict, Any

class RedisCache:
    def __init__(self):
        self.redis_client = None
        self.connected = False
    
    async def connect(self):
        try:
            import os

            # Try real Redis first
            try:
                redis_password = os.getenv('REDIS_PASSWORD')

                self.redis_client = redis.Redis(
                    host='localhost',
                    port=6379,
                    password=redis_password if redis_password else None,
                    decode_responses=True,
                    socket_connect_timeout=2,
                    socket_timeout=2
                )

                # Test connection
                self.redis_client.ping()
                self.connected = True
                print("Redis server connected successfully")
                return

            except Exception:
                # Fall back to FakeRedis (pure Python implementation)
                import fakeredis

                self.redis_client = fakeredis.FakeRedis(
                    decode_responses=True
                )

                # Test connection
                self.redis_client.ping()
                self.connected = True
                print("FakeRedis (Python Redis) connected successfully")

        except Exception as e:
            print(f"Redis connection failed: {e}")
            self.connected = False
    
    def get_cache_key(self, table: str, identifier: str) -> str:
        return f"blur:{table}:{identifier}"

    def get(self, key: str) -> Optional[Dict]:
        if not self.connected:
            return None

        try:
            redis_data = self.redis_client.get(key)
            if redis_data:
                return json.loads(redis_data)
        except Exception as e:
            print(f"Redis get error: {e}")

        return None
    
    def set(self, key: str, data: Dict, ttl: int = 3600):
        if not self.connected:
            return

        try:
            self.redis_client.setex(key, ttl, json.dumps(data))
        except Exception as e:
            print(f"Redis set error: {e}")

    def delete(self, key: str):
        if not self.connected:
            return

        try:
            self.redis_client.delete(key)
        except Exception as e:
            print(f"Redis delete error: {e}")
    
    def bulk_set(self, data_dict: Dict[str, Dict], ttl: int = 3600):
        if not self.connected:
            return

        try:
            pipe = self.redis_client.pipeline()
            for key, data in data_dict.items():
                pipe.setex(key, ttl, json.dumps(data))
            pipe.execute()
        except Exception as e:
            print(f"Redis bulk set error: {e}")

    def clear_pattern(self, pattern: str):
        if not self.connected:
            return

        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
        except Exception as e:
            print(f"Redis clear pattern error: {e}")

    def cleanup(self):
        # Redis handles TTL automatically, no manual cleanup needed
        pass

    def get_stats(self) -> Dict:
        stats = {
            "redis_connected": self.connected
        }

        if self.connected:
            try:
                info = self.redis_client.info('memory')
                stats.update({
                    "redis_memory_used": info.get('used_memory_human', 'Unknown'),
                    "redis_keys": self.redis_client.dbsize()
                })
            except Exception as e:
                print(f"Error getting Redis stats: {e}")

        return stats
