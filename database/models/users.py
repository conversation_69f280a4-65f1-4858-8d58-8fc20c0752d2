import mysql.connector
from typing import Optional, Dict, List
from datetime import datetime

class UserModel:
    def __init__(self, mysql_pool):
        self.mysql_pool = mysql_pool
    
    async def create_table(self):
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                user_id BIGINT,
                guild_id BIGINT,
                username VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                display_name VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                premium BOOLEAN DEFAULT FALSE,
                warnings INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (user_id, guild_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_settings (
                user_id BIGINT,
                guild_id BIGINT,
                notifications BOOLEAN DEFAULT TRUE,
                dm_commands BOOLEAN DEFAULT TRUE,
                timezone VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'UTC',
                PRIMARY KEY (user_id, guild_id),
                FOREIGN KEY (user_id, guild_id) REFERENCES users(user_id, guild_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.close()
        connection.close()
    
    def get_all_users(self) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute("SELECT * FROM users")
        users = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # Convert datetime to string for JSON serialization
        for user in users:
            if user.get('created_at'):
                user['created_at'] = user['created_at'].isoformat()
            if user.get('updated_at'):
                user['updated_at'] = user['updated_at'].isoformat()
        
        return users
    
    def get_user(self, user_id: int, guild_id: int) -> Optional[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute(
            "SELECT * FROM users WHERE user_id = %s AND guild_id = %s",
            (user_id, guild_id)
        )
        
        user = cursor.fetchone()
        cursor.close()
        connection.close()
        
        if user:
            if user.get('created_at'):
                user['created_at'] = user['created_at'].isoformat()
            if user.get('updated_at'):
                user['updated_at'] = user['updated_at'].isoformat()
        
        return user
    
    def create_user(self, user_id: int, username: str, display_name: str, guild_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
                INSERT INTO users (user_id, username, display_name, guild_id)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                username = VALUES(username),
                display_name = VALUES(display_name),
                updated_at = CURRENT_TIMESTAMP
            """, (user_id, username, display_name, guild_id))
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error creating user: {e}")
            return False
    
    def update_user_premium(self, user_id: int, guild_id: int, premium: bool) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute(
                "UPDATE users SET premium = %s WHERE user_id = %s AND guild_id = %s",
                (premium, user_id, guild_id)
            )
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error updating user premium: {e}")
            return False
    
    def add_warning(self, user_id: int, guild_id: int) -> int:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute(
                "UPDATE users SET warnings = warnings + 1 WHERE user_id = %s AND guild_id = %s",
                (user_id, guild_id)
            )
            
            cursor.execute(
                "SELECT warnings FROM users WHERE user_id = %s AND guild_id = %s",
                (user_id, guild_id)
            )
            
            result = cursor.fetchone()
            warnings = result[0] if result else 0
            
            cursor.close()
            connection.close()
            return warnings
        except Exception as e:
            print(f"Error adding warning: {e}")
            return 0
    
    def get_user_settings(self, user_id: int, guild_id: int) -> Optional[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute(
            "SELECT * FROM user_settings WHERE user_id = %s AND guild_id = %s",
            (user_id, guild_id)
        )
        
        settings = cursor.fetchone()
        cursor.close()
        connection.close()
        
        return settings
