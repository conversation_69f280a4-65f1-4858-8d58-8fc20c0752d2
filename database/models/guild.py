import mysql.connector
from typing import Optional, Dict, List

class GuildModel:
    def __init__(self, mysql_pool):
        self.mysql_pool = mysql_pool
    
    async def create_table(self):
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_settings (
                guild_id BIGINT PRIMARY KEY,
                prefix VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT ',',
                mod_log_channel BIGINT,
                welcome_channel BIGINT,
                auto_role BIGINT,
                premium_role BIGINT,
                mute_role BIGINT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_modules (
                guild_id BIGINT,
                module_name VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                enabled BOOLEAN DEFAULT TRUE,
                PRIMARY KEY (guild_id, module_name),
                FOREIGN KEY (guild_id) REFERENCES guild_settings(guild_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        cursor.close()
        connection.close()
    
    def get_all_guilds(self) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute("SELECT * FROM guild_settings")
        guilds = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # Convert datetime to string for JSON serialization
        for guild in guilds:
            if guild.get('created_at'):
                guild['created_at'] = guild['created_at'].isoformat()
            if guild.get('updated_at'):
                guild['updated_at'] = guild['updated_at'].isoformat()
        
        return guilds
    
    def get_guild(self, guild_id: int) -> Optional[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute(
            "SELECT * FROM guild_settings WHERE guild_id = %s",
            (guild_id,)
        )
        
        guild = cursor.fetchone()
        cursor.close()
        connection.close()
        
        if guild:
            if guild.get('created_at'):
                guild['created_at'] = guild['created_at'].isoformat()
            if guild.get('updated_at'):
                guild['updated_at'] = guild['updated_at'].isoformat()
        
        return guild
    
    def create_guild(self, guild_id: int, prefix: str = ',') -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
                INSERT INTO guild_settings (guild_id, prefix)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE
                updated_at = CURRENT_TIMESTAMP
            """, (guild_id, prefix))
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error creating guild: {e}")
            return False
    
    def update_guild_prefix(self, guild_id: int, prefix: str) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute(
                "UPDATE guild_settings SET prefix = %s WHERE guild_id = %s",
                (prefix, guild_id)
            )
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error updating guild prefix: {e}")
            return False
    
    def update_guild_channel(self, guild_id: int, channel_type: str, channel_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            valid_channels = ['mod_log_channel', 'welcome_channel']
            if channel_type not in valid_channels:
                return False
            
            cursor.execute(
                f"UPDATE guild_settings SET {channel_type} = %s WHERE guild_id = %s",
                (channel_id, guild_id)
            )
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error updating guild channel: {e}")
            return False
    
    def update_guild_role(self, guild_id: int, role_type: str, role_id: int) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            valid_roles = ['auto_role', 'premium_role', 'mute_role']
            if role_type not in valid_roles:
                return False
            
            cursor.execute(
                f"UPDATE guild_settings SET {role_type} = %s WHERE guild_id = %s",
                (role_id, guild_id)
            )
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error updating guild role: {e}")
            return False
    
    def get_guild_modules(self, guild_id: int) -> List[Dict]:
        connection = self.mysql_pool.get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute(
            "SELECT * FROM guild_modules WHERE guild_id = %s",
            (guild_id,)
        )
        
        modules = cursor.fetchall()
        cursor.close()
        connection.close()
        
        return modules
    
    def toggle_module(self, guild_id: int, module_name: str, enabled: bool) -> bool:
        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
                INSERT INTO guild_modules (guild_id, module_name, enabled)
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE
                enabled = VALUES(enabled)
            """, (guild_id, module_name, enabled))
            
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            print(f"Error toggling module: {e}")
            return False
