import discord
from discord.ext import commands
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.config import colors

# Color constants
COLORS = {
    'success': colors.success,
    'error': colors.error,
    'warn': colors.warn,
    'info': colors.default,
    'loading': colors.cooldown
}

class PrebuiltEmbeds:
    @staticmethod
    async def success(ctx: commands.Context, text: str) -> discord.Message:
        """Send a success embed"""
        embed = discord.Embed(
            color=COLORS['success'],
            description=f"<@{ctx.author.id}>: {text}"
        )
        return await ctx.send(embed=embed)
    
    @staticmethod
    async def deny(ctx: commands.Context, text: str) -> discord.Message:
        """Send an error/deny embed"""
        embed = discord.Embed(
            color=COLORS['error'],
            description=f"<@{ctx.author.id}>: {text}"
        )
        return await ctx.send(embed=embed)
    
    @staticmethod
    async def warn(ctx: commands.Context, text: str) -> discord.Message:
        """Send a warning embed"""
        embed = discord.Embed(
            color=COLORS['warn'],
            description=f"<@{ctx.author.id}>: {text}"
        )
        return await ctx.send(embed=embed)
    
    @staticmethod
    async def default(ctx: commands.Context, text: str) -> discord.Message:
        """Send a default/info embed"""
        embed = discord.Embed(
            color=COLORS['info'],
            description=f"<@{ctx.author.id}>: {text}"
        )
        return await ctx.send(embed=embed)
    
    @staticmethod
    async def loading(ctx: commands.Context, text: str) -> discord.Message:
        """Send a loading embed"""
        embed = discord.Embed(
            color=COLORS['loading'],
            description=f"<@{ctx.author.id}>: {text}"
        )
        return await ctx.send(embed=embed)

# Create a global instance for easy importing
embeds = PrebuiltEmbeds()
