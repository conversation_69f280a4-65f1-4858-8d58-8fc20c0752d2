import discord
from discord.ext import commands
from typing import Optional, List, Callable, Any

class ButtonBuilder:
    def __init__(self):
        self.buttons = []
    
    def add_button(self, label: str, style: discord.ButtonStyle, custom_id: str = None, 
                   url: str = None, emoji: str = None, disabled: bool = False) -> 'ButtonBuilder':
        """Add a button to the builder"""
        button = discord.ui.Button(
            label=label,
            style=style,
            custom_id=custom_id,
            url=url,
            emoji=emoji,
            disabled=disabled
        )
        self.buttons.append(button)
        return self
    
    def add_link_button(self, label: str, url: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a link button"""
        return self.add_button(label, discord.ButtonStyle.link, url=url, emoji=emoji)
    
    def add_blue_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a blue/primary button"""
        return self.add_button(label, discord.ButtonStyle.primary, custom_id=custom_id, emoji=emoji)
    
    def add_green_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a green/success button"""
        return self.add_button(label, discord.ButtonStyle.success, custom_id=custom_id, emoji=emoji)
    
    def add_red_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a red/danger button"""
        return self.add_button(label, discord.ButtonStyle.danger, custom_id=custom_id, emoji=emoji)
    
    def add_gray_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a gray/secondary button"""
        return self.add_button(label, discord.ButtonStyle.secondary, custom_id=custom_id, emoji=emoji)
    
    def build(self) -> discord.ui.View:
        """Build the view with all buttons"""
        view = discord.ui.View()
        for button in self.buttons:
            view.add_item(button)
        return view
    
    def clear(self) -> 'ButtonBuilder':
        """Clear all buttons"""
        self.buttons = []
        return self

class SimpleButton(discord.ui.Button):
    def __init__(self, label: str, style: discord.ButtonStyle, callback_func: Callable = None, **kwargs):
        super().__init__(label=label, style=style, **kwargs)
        self.callback_func = callback_func
    
    async def callback(self, interaction: discord.Interaction):
        if self.callback_func:
            await self.callback_func(interaction)

class ConfirmView(discord.ui.View):
    def __init__(self, timeout: float = 60.0):
        super().__init__(timeout=timeout)
        self.value = None
    
    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.green)
    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = True
        self.stop()
    
    @discord.ui.button(label='Cancel', style=discord.ButtonStyle.red)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = False
        self.stop()

class PaginationView(discord.ui.View):
    def __init__(self, pages: List[discord.Embed], author_id: int, timeout: float = 180.0):
        super().__init__(timeout=timeout)
        self.pages = pages
        self.current_page = 0
        self.max_pages = len(pages)
        self.author_id = author_id

        # Import emotes
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
        from utils.config import emotes
        self.emotes = emotes

        # Update button emojis
        self.previous_page.emoji = self.emotes.left
        self.cancel_pagination.emoji = self.emotes.cancel
        self.next_page.emoji = self.emotes.right
        self.select_page.emoji = self.emotes.choose

        # Disable buttons if only one page
        if self.max_pages <= 1:
            self.previous_page.disabled = True
            self.next_page.disabled = True
            self.select_page.disabled = True

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("You cannot use this pagination.", ephemeral=True)
            return False
        return True

    @discord.ui.button(style=discord.ButtonStyle.secondary)
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
        else:
            # Wrap to last page
            self.current_page = self.max_pages - 1
        await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

    @discord.ui.button(style=discord.ButtonStyle.secondary)
    async def cancel_pagination(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.edit_message(view=None)

    @discord.ui.button(style=discord.ButtonStyle.secondary)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < self.max_pages - 1:
            self.current_page += 1
        else:
            # Wrap to first page
            self.current_page = 0
        await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

    @discord.ui.button(style=discord.ButtonStyle.secondary)
    async def select_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = PageSelectModal(self)
        await interaction.response.send_modal(modal)

class PageSelectModal(discord.ui.Modal):
    def __init__(self, pagination_view):
        super().__init__(title="Select Page")
        self.pagination_view = pagination_view

        self.page_input = discord.ui.TextInput(
            label="Page Number",
            placeholder=f"Enter page number (1-{pagination_view.max_pages})",
            min_length=1,
            max_length=3
        )
        self.add_item(self.page_input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            page_num = int(self.page_input.value)
            if 1 <= page_num <= self.pagination_view.max_pages:
                self.pagination_view.current_page = page_num - 1
                await interaction.response.edit_message(
                    embed=self.pagination_view.pages[self.pagination_view.current_page],
                    view=self.pagination_view
                )
            else:
                await interaction.response.send_message(
                    f"Invalid page number. Please enter a number between 1 and {self.pagination_view.max_pages}.",
                    ephemeral=True
                )
        except ValueError:
            await interaction.response.send_message(
                "Please enter a valid number.",
                ephemeral=True
            )

def create_button_builder() -> ButtonBuilder:
    """Create a new button builder"""
    return ButtonBuilder()

def create_confirm_view(timeout: float = 60.0) -> ConfirmView:
    """Create a confirm/cancel view"""
    return ConfirmView(timeout=timeout)

def create_pagination_view(pages: List[discord.Embed], author_id: int, timeout: float = 180.0) -> PaginationView:
    """Create a pagination view for multiple embeds"""
    return PaginationView(pages, author_id, timeout=timeout)
