import discord
from discord.ext import commands
from typing import Optional, List, Callable, Any

class ButtonBuilder:
    def __init__(self):
        self.buttons = []
    
    def add_button(self, label: str, style: discord.ButtonStyle, custom_id: str = None, 
                   url: str = None, emoji: str = None, disabled: bool = False) -> 'ButtonBuilder':
        """Add a button to the builder"""
        button = discord.ui.Button(
            label=label,
            style=style,
            custom_id=custom_id,
            url=url,
            emoji=emoji,
            disabled=disabled
        )
        self.buttons.append(button)
        return self
    
    def add_link_button(self, label: str, url: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a link button"""
        return self.add_button(label, discord.ButtonStyle.link, url=url, emoji=emoji)
    
    def add_blue_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a blue/primary button"""
        return self.add_button(label, discord.ButtonStyle.primary, custom_id=custom_id, emoji=emoji)
    
    def add_green_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a green/success button"""
        return self.add_button(label, discord.ButtonStyle.success, custom_id=custom_id, emoji=emoji)
    
    def add_red_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a red/danger button"""
        return self.add_button(label, discord.ButtonStyle.danger, custom_id=custom_id, emoji=emoji)
    
    def add_gray_button(self, label: str, custom_id: str, emoji: str = None) -> 'ButtonBuilder':
        """Add a gray/secondary button"""
        return self.add_button(label, discord.ButtonStyle.secondary, custom_id=custom_id, emoji=emoji)
    
    def build(self) -> discord.ui.View:
        """Build the view with all buttons"""
        view = discord.ui.View()
        for button in self.buttons:
            view.add_item(button)
        return view
    
    def clear(self) -> 'ButtonBuilder':
        """Clear all buttons"""
        self.buttons = []
        return self

class SimpleButton(discord.ui.Button):
    def __init__(self, label: str, style: discord.ButtonStyle, callback_func: Callable = None, **kwargs):
        super().__init__(label=label, style=style, **kwargs)
        self.callback_func = callback_func
    
    async def callback(self, interaction: discord.Interaction):
        if self.callback_func:
            await self.callback_func(interaction)

class ConfirmView(discord.ui.View):
    def __init__(self, timeout: float = 60.0):
        super().__init__(timeout=timeout)
        self.value = None
    
    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.green)
    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = True
        self.stop()
    
    @discord.ui.button(label='Cancel', style=discord.ButtonStyle.red)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = False
        self.stop()

class PaginationView(discord.ui.View):
    def __init__(self, pages: List[discord.Embed], timeout: float = 180.0):
        super().__init__(timeout=timeout)
        self.pages = pages
        self.current_page = 0
        self.max_pages = len(pages)
        
        # Disable buttons if only one page
        if self.max_pages <= 1:
            self.previous_page.disabled = True
            self.next_page.disabled = True
    
    @discord.ui.button(label='◀', style=discord.ButtonStyle.gray)
    async def previous_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
            await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)
    
    @discord.ui.button(label='▶', style=discord.ButtonStyle.gray)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < self.max_pages - 1:
            self.current_page += 1
            await interaction.response.edit_message(embed=self.pages[self.current_page], view=self)

def create_button_builder() -> ButtonBuilder:
    """Create a new button builder"""
    return ButtonBuilder()

def create_confirm_view(timeout: float = 60.0) -> ConfirmView:
    """Create a confirm/cancel view"""
    return ConfirmView(timeout=timeout)

def create_pagination_view(pages: List[discord.Embed], timeout: float = 180.0) -> PaginationView:
    """Create a pagination view for multiple embeds"""
    return PaginationView(pages, timeout=timeout)
