import discord
from discord.ext import commands
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from embeds.parse import EmbedBuilder, create_embed
from utils.permissions import PermissionCheck, PermissionErrorHandler

class EmbedCodeView(discord.ui.View):
    def __init__(self, embed_code: str, timeout: float = 300.0):
        super().__init__(timeout=timeout)
        self.embed_code = embed_code

    @discord.ui.button(label='Link', style=discord.ButtonStyle.secondary, emoji='🔗')
    async def send_link(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_message(
            f"```{self.embed_code}```",
            ephemeral=True
        )

class Utility(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await Permission<PERSON>rror<PERSON><PERSON><PERSON>(ctx, "permission")

    @commands.command(
        name="createembed",
        description="Create custom embeds using embed code syntax",
        usage="createembed <embed_code>",
        example="createembed {title: Welcome}$v{description: Hello everyone}",
        aliases=['ce', 'embed']
    )
    @PermissionCheck.discord_permission("ManageMessages")
    async def createembed(self, ctx, *, embed_code: str = None):
        """Create custom embeds using embed code syntax"""
        
        if not embed_code:
            help_embed = create_embed(
                title="Embed Creator Help",
                description="Create custom embeds using this syntax:",
                color=0x00ff00
            )
            
            help_embed.add_field(
                name="Basic Structure",
                value="```{title: Your Title}$v{description: Your Description}$v{color: #ff0000}```",
                inline=False
            )
            
            help_embed.add_field(
                name="Available Parameters",
                value="`title:` `description:` `color:` `thumbnail:` `image:` `url:` `timestamp`",
                inline=False
            )
            
            help_embed.add_field(
                name="Field Syntax",
                value="```{field: name: Field Name && value: Field Value && inline}```",
                inline=False
            )
            
            help_embed.add_field(
                name="Author Syntax",
                value="```{author: name: Author Name && icon: icon_url && url: author_url}```",
                inline=False
            )
            
            help_embed.add_field(
                name="Footer Syntax",
                value="```{footer: text: Footer Text && icon: icon_url}```",
                inline=False
            )
            
            help_embed.add_field(
                name="Button Syntax",
                value="```{button: link && Label && https://example.com}\n{button: blue && Click Me}\n{button: green && Success}\n{button: red && Danger}\n{button: gray && Secondary}```",
                inline=False
            )
            
            help_embed.add_field(
                name="Example",
                value="```{title: Welcome!}$v{description: This is a test embed}$v{color: blue}$v{field: name: Test Field && value: Test Value}$v{timestamp}```",
                inline=False
            )
            
            await ctx.send(embed=help_embed)
            return
        
        try:
            builder = EmbedBuilder(ctx)
            result = builder.parse_embed_string(embed_code)
            
            # Send the result (embed and/or content)
            embed = result.get('embed')
            content = result.get('content')
            view = result.get('view')

            # Make sure we have something to send
            if not embed and not content:
                await ctx.send("No content to send!", delete_after=5)
                return

            await ctx.send(
                content=content,
                embed=embed,
                view=view
            )
                
        except Exception as e:
            error_embed = create_embed(
                title="Embed Creation Error",
                description=f"Failed to create embed:\n```{str(e)}```",
                color=0xff0000
            )
            await ctx.send(embed=error_embed, delete_after=10)

    @commands.command()
    @PermissionCheck.discord_permission("ManageMessages")
    async def embedtest(self, ctx):
        
        test_code = "{title: Test Embed}$v{description: This is a test embed to verify the system works!}$v{color: #00ff00}$v{field: name: Test Field && value: This is a test field}$v{footer: text: Test Footer}$v{timestamp}"
        
        builder = EmbedBuilder()
        result = builder.parse_embed_string(test_code)
        
        await ctx.send(
            content=result.get('content'),
            embed=result.get('embed'),
            view=result.get('view')
        )

    @commands.command()
    @PermissionCheck.discord_permission("ManageMessages")
    async def say(self, ctx, *, message: str):
        """Make the bot say something"""
        try:
            await ctx.message.delete()
        except:
            pass
        
        await ctx.send(message)

    @commands.command()
    @PermissionCheck.discord_permission("ManageMessages")
    async def edit(self, ctx, message_id: int, *, new_content: str):
        """Edit a bot message"""
        try:
            message = await ctx.channel.fetch_message(message_id)
            if message.author != self.bot.user:
                await ctx.send("I can only edit my own messages!", delete_after=5)
                return
            
            # Check if it's an embed edit
            if new_content.startswith('{') and '}' in new_content:
                builder = EmbedBuilder()
                result = builder.parse_embed_string(new_content)
                
                await message.edit(
                    content=result.get('content'),
                    embed=result.get('embed'),
                    view=result.get('view')
                )
            else:
                await message.edit(content=new_content)
            
            await ctx.send("Message edited successfully!", delete_after=3)
            
        except discord.NotFound:
            await ctx.send("Message not found!", delete_after=5)
        except Exception as e:
            await ctx.send(f"Error editing message: {e}", delete_after=5)

    @commands.command(
        name="copyembed",
        description="Get the embed code from a message",
        usage="copyembed <message_link_or_id>",
        example="copyembed https://discord.com/channels/123/456/789",
        aliases=['copy']
    )
    @PermissionCheck.discord_permission("ManageMessages")
    async def copyembed(self, ctx, message_link_or_id: str):
        """Copy embed code from a message"""
        try:
            # Parse message link or ID
            if message_link_or_id.startswith('https://discord.com/channels/'):
                # Extract IDs from message link
                parts = message_link_or_id.split('/')
                if len(parts) >= 3:
                    channel_id = int(parts[-2])
                    message_id = int(parts[-1])
                    channel = self.bot.get_channel(channel_id)
                else:
                    await ctx.send("Invalid message link format", delete_after=5)
                    return
            else:
                # Assume it's a message ID in current channel
                try:
                    message_id = int(message_link_or_id)
                    channel = ctx.channel
                except ValueError:
                    await ctx.send("Invalid message ID", delete_after=5)
                    return

            if not channel:
                await ctx.send("Channel not found or I don't have access", delete_after=5)
                return

            # Fetch the message
            try:
                message = await channel.fetch_message(message_id)
            except discord.NotFound:
                await ctx.send("Message not found", delete_after=5)
                return
            except discord.Forbidden:
                await ctx.send("I don't have permission to read that message", delete_after=5)
                return

            # Check if message has embeds
            if not message.embeds:
                await ctx.send("That message doesn't have any embeds", delete_after=5)
                return

            embed = message.embeds[0]  # Get first embed

            # Convert embed to code
            embed_code_parts = []

            # Title
            if embed.title:
                embed_code_parts.append(f"{{title: {embed.title}}}")

            # Description
            if embed.description:
                embed_code_parts.append(f"{{description: {embed.description}}}")

            # Color
            if embed.color:
                embed_code_parts.append(f"{{color: #{embed.color.value:06x}}}")

            # Author
            if embed.author:
                author_parts = []
                if embed.author.name:
                    author_parts.append(f"name: {embed.author.name}")
                if embed.author.icon_url:
                    author_parts.append(f"icon: {embed.author.icon_url}")
                if embed.author.url:
                    author_parts.append(f"url: {embed.author.url}")
                if author_parts:
                    embed_code_parts.append(f"{{author: {' && '.join(author_parts)}}}")

            # Fields
            for field in embed.fields:
                field_parts = [f"name: {field.name}", f"value: {field.value}"]
                if field.inline:
                    field_parts.append("inline")
                embed_code_parts.append(f"{{field: {' && '.join(field_parts)}}}")

            # Footer
            if embed.footer:
                footer_parts = []
                if embed.footer.text:
                    footer_parts.append(f"text: {embed.footer.text}")
                if embed.footer.icon_url:
                    footer_parts.append(f"icon: {embed.footer.icon_url}")
                if footer_parts:
                    embed_code_parts.append(f"{{footer: {' && '.join(footer_parts)}}}")

            # Thumbnail
            if embed.thumbnail:
                embed_code_parts.append(f"{{thumbnail: {embed.thumbnail.url}}}")

            # Image
            if embed.image:
                embed_code_parts.append(f"{{image: {embed.image.url}}}")

            # Timestamp
            if embed.timestamp:
                embed_code_parts.append("{timestamp}")

            # URL
            if embed.url:
                embed_code_parts.append(f"{{url: {embed.url}}}")

            # Join with $v
            embed_code = "$v".join(embed_code_parts)

            if not embed_code:
                await ctx.send("Could not extract embed code", delete_after=5)
                return

            # Create embed to display the code
            code_embed = create_embed(
                title="Embed Code Extracted",
                description=f"```{embed_code[:1900] if len(embed_code) > 1900 else embed_code}```",
                color=0xa4ec7c
            )

            if len(embed_code) > 1900:
                code_embed.set_footer(text="Code truncated - use Link button for full code")

            # Create view with link button
            view = EmbedCodeView(embed_code)
            await ctx.send(embed=code_embed, view=view)

        except Exception as e:
            await ctx.send(f"Error copying embed: {e}", delete_after=5)

async def setup(bot):
    await bot.add_cog(Utility(bot))
