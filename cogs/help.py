import discord
from discord.ext import commands
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from embeds.parse import create_embed, EmbedBuilder
from embeds.prebuilt import embeds
from embeds.button import create_pagination_view
from utils.config import emotes, config, colors

class Help(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def get_command_info(self, command):
        """Extract command information from command object"""
        info = {
            'name': command.name,
            'description': getattr(command, 'description', 'No description available'),
            'usage': getattr(command, 'usage', f"{command.name} [args]"),
            'example': getattr(command, 'example', f"{command.name}"),
            'aliases': getattr(command, 'aliases', []),
            'permissions': 'None'
        }

        # Extract permission from decorators
        for check in command.checks:
            if hasattr(check, '__name__') and 'discord_permission' in str(check):
                # Try to extract permission name from the check
                try:
                    import inspect
                    source = inspect.getsource(check)
                    if 'discord_permission' in source:
                        perm_start = source.find('"') + 1
                        perm_end = source.find('"', perm_start)
                        if perm_start > 0 and perm_end > perm_start:
                            info['permissions'] = source[perm_start:perm_end]
                except:
                    pass

        return info

    @commands.command(
        name="help",
        description="Show help for commands",
        usage="help [command]",
        example="help ban",
        aliases=['h']
    )
    async def help(self, ctx, *, command_name: str = None):
        """Show help for commands"""

        if not command_name:
            # Show development message with support server button only when no command specified
            embed = create_embed(
                description="Blur is in **beta** right now. If you need help or have some issues, Join the **official server** via the button below.",
                color=colors.default
            )

            # Create view with support server button
            view = discord.ui.View()
            button = discord.ui.Button(
                label="Official Server",
                style=discord.ButtonStyle.link,
                url=config.support_server
            )
            view.add_item(button)

            await ctx.send(embed=embed, view=view)
            return

        # Find the command
        command = self.bot.get_command(command_name.lower())
        if not command:
            await embeds.deny(ctx, f"Command `{command_name}` not found")
            return

        # Check if it's a group command
        if isinstance(command, commands.Group):
            await self.show_group_help(ctx, command)
        else:
            # Show specific command help
            await self.show_command_help(ctx, command)

    async def show_general_help(self, ctx):
        """Show general help with command categories"""
        # Get all commands grouped by cog
        cog_commands = {}
        for command in self.bot.commands:
            if command.cog and command.cog.__class__.__name__ != 'Help':
                cog_name = command.cog.__class__.__name__
                if cog_name not in cog_commands:
                    cog_commands[cog_name] = []
                cog_commands[cog_name].append(command)

        # Create main help embed
        main_embed = create_embed(
            title=f"{emotes.choose} Bot Help",
            description="Use `,help <command>` or `,h <command>` for specific command help",
            color=colors.default
        )

        for cog_name, commands in cog_commands.items():
            cmd_names = [f"`{cmd.name}`" for cmd in commands]
            main_embed.add_field(
                name=f"{cog_name} Commands",
                value=", ".join(cmd_names),
                inline=False
            )

        main_embed.add_field(
            name="Examples",
            value="`,h mute` - Show mute command help\n`,h createembed` - Show embed creation help",
            inline=False
        )

        await ctx.send(embed=main_embed)

    async def show_group_help(self, ctx, group):
        """Show help for a command group with pagination"""
        subcommands = list(group.commands)

        if not subcommands:
            await embeds.deny(ctx, f"No subcommands found for `{group.name}`")
            return

        # Create pages for subcommands
        pages = []
        for subcommand in subcommands:
            info = self.get_command_info(subcommand)
            aliases_text = ", ".join(info['aliases']) if info['aliases'] else "n/a"

            # Create embed using the custom format
            embed_code = f"""{{author: name: {ctx.author.display_name} && icon: {ctx.author.display_avatar.url}}}$v{{title: Command: {group.name} {info['name']}}}$v{{description: {info['description']}}}$v{{field: name: **Aliases** && value: {aliases_text} && inline}}$v{{field: name: **Information** && value: {info['permissions']} && inline}}$v{{field: name: **Usage** && value: ```Syntax: {info['usage']}
Example: {info['example']}```}}"""

            try:
                builder = EmbedBuilder(ctx)
                result = builder.parse_embed_string(embed_code)
                if result.get('embed'):
                    pages.append(result['embed'])
            except Exception:
                # Fallback embed
                embed = create_embed(
                    title=f"Command: {group.name} {info['name']}",
                    description=info['description'],
                    color=colors.default
                )
                embed.set_author(name=ctx.author.display_name, icon_url=ctx.author.display_avatar.url)
                embed.add_field(name="Aliases", value=aliases_text, inline=True)
                embed.add_field(name="Information", value=info['permissions'], inline=True)
                embed.add_field(name="Usage", value=f"```Syntax: {info['usage']}\nExample: {info['example']}```", inline=False)
                pages.append(embed)

        if len(pages) == 1:
            await ctx.send(embed=pages[0])
        else:
            # Add page numbers to embeds
            for i, page in enumerate(pages, 1):
                page.set_footer(text=f"Page {i}/{len(pages)}")

            view = create_pagination_view(pages, ctx.author.id)
            await ctx.send(embed=pages[0], view=view)

    async def show_command_help(self, ctx, command):
        """Show detailed help for a specific command"""
        info = self.get_command_info(command)

        # Format aliases
        aliases_text = ", ".join(info['aliases']) if info['aliases'] else "n/a"

        # Create embed using the custom format
        embed_code = f"""{{author: name: {ctx.author.display_name} && icon: {ctx.author.display_avatar.url}}}$v{{title: Command: {info['name']}}}$v{{description: {info['description']}}}$v{{field: name: **Aliases** && value: {aliases_text} && inline}}$v{{field: name: **Information** && value: {info['permissions']} && inline}}$v{{field: name: **Usage** && value: ```Syntax: {info['usage']}
Example: {info['example']}```}}"""

        try:
            builder = EmbedBuilder(ctx)
            result = builder.parse_embed_string(embed_code)

            await ctx.send(
                content=result.get('content'),
                embed=result.get('embed'),
                view=result.get('view')
            )
        except Exception as e:
            # Fallback to simple embed if parsing fails
            embed = create_embed(
                title=f"Command: {info['name']}",
                description=info['description'],
                color=colors.default
            )
            embed.set_author(name=ctx.author.display_name, icon_url=ctx.author.display_avatar.url)
            embed.add_field(name="Aliases", value=aliases_text, inline=True)
            embed.add_field(name="Information", value=info['permissions'], inline=True)
            embed.add_field(name="Usage", value=f"```Syntax: {info['usage']}\nExample: {info['example']}```", inline=False)
            await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(Help(bot))
