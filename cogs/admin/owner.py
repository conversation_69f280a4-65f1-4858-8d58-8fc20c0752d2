import discord
from discord.ext import commands
import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from utils.permissions import PermissionCheck, PermissionErrorHandler
from embeds.parse import create_embed
from embeds.prebuilt import embeds
from utils.finder import FindUser
from utils.config import colors

class Owner(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await PermissionErrorHandler(ctx, "owner")

    @commands.command()
    @PermissionCheck.owner_only()
    async def reload(self, ctx, *, cog: str = None):
        if not cog:
            await ctx.send("Specify a cog to reload")
            return

        try:
            await self.bot.reload_extension(f'cogs.admin.{cog}')
            await ctx.send(f"Reloaded: {cog}")
        except Exception as e:
            await ctx.send(f"Failed to reload {cog}: {e}")

    @commands.command()
    @PermissionCheck.owner_only()
    async def load(self, ctx, *, cog: str):
        try:
            await self.bot.load_extension(f'cogs.admin.{cog}')
            await ctx.send(f"Loaded: {cog}")
        except Exception as e:
            await ctx.send(f"Failed to load {cog}: {e}")

    @commands.command()
    @PermissionCheck.owner_only()
    async def unload(self, ctx, *, cog: str):
        try:
            await self.bot.unload_extension(f'cogs.admin.{cog}')
            await ctx.send(f"Unloaded: {cog}")
        except Exception as e:
            await ctx.send(f"Failed to unload {cog}: {e}")

    @commands.command()
    @PermissionCheck.owner_only()
    async def shutdown(self, ctx):
        await ctx.send("Shutting down...")
        await self.bot.close()

    @commands.command()
    @PermissionCheck.owner_only()
    async def sync(self, ctx):
        synced = await self.bot.tree.sync()
        await ctx.send(f"Synced {len(synced)} commands")

    @commands.group(
        name="sudo",
        description="Owner-only administrative commands",
        usage="sudo <subcommand>",
        example="sudo mutuals @user",
        invoke_without_command=True
    )
    @PermissionCheck.owner_only()
    async def sudo(self, ctx):
        if ctx.invoked_subcommand is None:
            # Show help for sudo group
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_group_help(ctx, ctx.command)

    @sudo.command(
        name="mutuals",
        description="List all mutual servers between the bot and a user",
        usage="sudo mutuals <user>",
        example="sudo mutuals @user",
        aliases=['mutual']
    )
    @PermissionCheck.owner_only()
    async def mutuals(self, ctx, *, user_input: str = None):
        if not user_input:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Find the user
        user = await FindUser(ctx, user_input)
        if not user:
            await embeds.deny(ctx, "Could not find the specified user.")
            return

        # Get mutual servers
        mutual_servers = []
        for guild in self.bot.guilds:
            if guild.get_member(user.id):
                mutual_servers.append(guild)

        if not mutual_servers:
            await embeds.warn(ctx, f"No mutual servers found with **{user.name}**.")
            return

        # Create embed with mutual servers
        embed = create_embed(
            title=f"Mutuals of {user.name}",
            color=colors.default
        )
        embed.set_author(name=user.name, icon_url=user.display_avatar.url)

        description_lines = []
        for i, guild in enumerate(mutual_servers, 1):
            description_lines.append(f"`{i}` **{guild.name}**(`{guild.id}`)")

        embed.description = "\n".join(description_lines)
        embed.set_footer(text=f"{len(mutual_servers)} mutual servers")

        await ctx.send(embed=embed)

    @sudo.command(
        name="invite",
        description="Get an invite link for a server the bot is in",
        usage="sudo invite <server_id>",
        example="sudo invite 123456789",
        aliases=['inv']
    )
    @PermissionCheck.owner_only()
    async def invite(self, ctx, server_id: int = None):
        if server_id is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Find the guild
        guild = self.bot.get_guild(server_id)
        if not guild:
            await embeds.deny(ctx, f"Bot is not in a server with ID `{server_id}`.")
            return

        # Try to create an invite
        try:
            # Find a suitable channel to create invite from
            invite_channel = None

            # Try to find a general channel first
            for channel in guild.text_channels:
                if channel.permissions_for(guild.me).create_instant_invite:
                    if any(name in channel.name.lower() for name in ['general', 'main', 'chat']):
                        invite_channel = channel
                        break

            # If no general channel, use first available channel
            if not invite_channel:
                for channel in guild.text_channels:
                    if channel.permissions_for(guild.me).create_instant_invite:
                        invite_channel = channel
                        break

            if not invite_channel:
                await embeds.deny(ctx, f"No permission to create invites in **{guild.name}**(`{guild.id}`).")
                return

            # Create the invite
            invite = await invite_channel.create_invite(
                max_age=0,  # Never expires
                max_uses=0,  # Unlimited uses
                reason=f"Invite requested by {ctx.author}"
            )

            await embeds.default(ctx, f"Here's the [invite link]({invite.url}) of the server **{guild.name}**(`{guild.id}`)")

        except discord.Forbidden:
            await embeds.deny(ctx, f"No permission to create invites in **{guild.name}**(`{guild.id}`).")
        except Exception as e:
            await embeds.deny(ctx, f"Failed to create invite for **{guild.name}**(`{guild.id}`): {str(e)}")

    @sudo.command(
        name="leave",
        description="Make the bot leave a server",
        usage="sudo leave <server_id>",
        example="sudo leave 123456789"
    )
    @PermissionCheck.owner_only()
    async def leave(self, ctx, server_id: int = None):
        if server_id is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Find the guild
        guild = self.bot.get_guild(server_id)
        if not guild:
            await embeds.deny(ctx, f"Bot is not in a server with ID `{server_id}`.")
            return

        guild_name = guild.name
        guild_id = guild.id

        try:
            await guild.leave()
            await embeds.success(ctx, f"**blur** successfully left the server **{guild_name}**(`{guild_id}`)")

        except Exception as e:
            await embeds.deny(ctx, f"Failed to leave **{guild_name}**(`{guild_id}`): {str(e)}")

async def setup(bot):
    await bot.add_cog(Owner(bot))
