import discord
from discord.ext import commands
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from utils.permissions import PermissionCheck, PermissionErrorHandler
from embeds.prebuilt import embeds
from utils.finder import <PERSON><PERSON><PERSON>
from utils.role_security import RoleSecurity
from database.database import db

class ButtonRoleView(discord.ui.View):
    def __init__(self, roles_data, timeout=None):
        super().__init__(timeout=timeout)
        self.roles_data = roles_data
        

        for i, role_data in enumerate(roles_data):
            button = ButtonRoleButton(
                label=role_data.get('label'),
                style=self.get_button_style(role_data['color']),
                emoji=role_data.get('emoji'),
                role_id=role_data['role_id'],
                custom_id=f"button_role_{role_data['role_id']}"
            )
            self.add_item(button)
    
    def get_button_style(self, color):
        color_map = {
            'blue': discord.ButtonStyle.primary,
            'gray': discord.ButtonStyle.secondary,
            'grey': discord.ButtonStyle.secondary,
            'green': discord.ButtonStyle.success,
            'red': discord.ButtonStyle.danger
        }
        return color_map.get(color.lower(), discord.ButtonStyle.secondary)

class ButtonRoleButton(discord.ui.Button):
    def __init__(self, label, style, emoji, role_id, custom_id):

        super().__init__(
            label=label if label else None,
            style=style,
            emoji=emoji if emoji else None,
            custom_id=custom_id
        )
        self.role_id = role_id
    
    async def callback(self, interaction: discord.Interaction):
        role = interaction.guild.get_role(self.role_id)
        if not role:
            await interaction.response.send_message("Role not found.", ephemeral=True)
            return
        
        member = interaction.user
        if role in member.roles:
            try:
                await member.remove_roles(role, reason="Button role removal")
                await interaction.response.send_message(f"Removed role: {role.mention}", ephemeral=True)
            except discord.Forbidden:
                await interaction.response.send_message("I don't have permission to remove this role.", ephemeral=True)
        else:
            try:
                await member.add_roles(role, reason="Button role assignment")
                await interaction.response.send_message(f"Added role: {role.mention}", ephemeral=True)
            except discord.Forbidden:
                await interaction.response.send_message("I don't have permission to add this role.", ephemeral=True)

class ButtonRole(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await PermissionErrorHandler(ctx, "permission")

    @commands.group(
        name="buttonrole",
        description="Set up self-assignable roles with buttons",
        usage="buttonrole <subcommand> <args>",
        example="buttonrole add .../channels/... gray 🌟 Star",
        aliases=[]
    )
    @PermissionCheck.discord_permission("ManageGuild")
    async def buttonrole(self, ctx):
        if ctx.invoked_subcommand is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_group_help(ctx, ctx.command)

    @buttonrole.command(
        name="add",
        description="Add a button role to a message (emoji and/or label required)",
        usage="buttonrole add <message_link> <color> [emoji] [label] <role>",
        example="buttonrole add .../channels/... gray 🌟 \"Star Role\" @role"
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def br_add(self, ctx, message_link: str = None, color: str = None, *args):
        if len(args) < 1:  # At minimum need role
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse arguments - last argument is always the role
        role_input = args[-1]
        remaining_args = args[:-1]

        emoji = None
        label = None

        # Parse emoji and label from remaining arguments
        for arg in remaining_args:
            # Check if it's an emoji (contains emoji characters or is a custom emoji)
            if any(ord(char) > 127 for char in arg) or (arg.startswith('<') and arg.endswith('>')):
                emoji = arg
            else:
                # It's text, combine with existing label if any
                if label:
                    label += f" {arg}"
                else:
                    label = arg

        # Must have at least emoji or label
        if not emoji and not label:
            await embeds.deny(ctx, "You must provide at least an emoji or label for the button.")
            return

        if not all([message_link, color, role_input]):
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Validate color
        valid_colors = ['blue', 'gray', 'green', 'red']
        if color.lower() not in valid_colors:
            await embeds.deny(ctx, f"Invalid button color used, choose one of them **{', '.join(valid_colors)}**")
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        # Find role
        role = await FindRole(ctx, role_input)
        if not role:
            await embeds.deny(ctx, f"Could not find role: {role_input}")
            return

        # Security checks
        can_assign, error_reason = RoleSecurity.can_user_assign_role(ctx.author, role, ctx.guild.me)
        if not can_assign:
            await PermissionErrorHandler(ctx, "role_security", error_reason)
            return

        try:
            # Get existing button roles for this message
            existing_roles = db.guilds.get_button_roles(ctx.guild.id, channel.id, message.id)

            # Calculate next index
            next_index = len(existing_roles) + 1

            # Store in database first
            success = db.guilds.add_button_role(
                ctx.guild.id, channel.id, message.id, role.id,
                color, emoji or "", label or "", next_index, ctx.author.id
            )

            if not success:
                await embeds.deny(ctx, "Failed to save button role to database.")
                return

            # Get updated roles from database
            updated_roles = db.guilds.get_button_roles(ctx.guild.id, channel.id, message.id)

            # Convert to format expected by ButtonRoleView
            role_data = []
            for br in updated_roles:
                role_data.append({
                    'role_id': br['role_id'],
                    'color': br['button_color'],
                    'emoji': br['button_emoji'] if br['button_emoji'] else None,
                    'label': br['button_label'] if br['button_label'] else None
                })

            # Create new view with updated buttons
            view = ButtonRoleView(role_data)

            # Edit message with new view
            await message.edit(view=view)

            # Create display text for what was added
            display_parts = []
            if emoji:
                display_parts.append(emoji)
            if label:
                display_parts.append(label)

            display_text = " ".join(display_parts) if display_parts else "Button"
            await embeds.success(ctx, f"Added button role: {display_text} → {role.mention}")

        except discord.HTTPException as e:
            await embeds.deny(ctx, f"Failed to edit message: {str(e)}")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @buttonrole.command(
        name="remove",
        description="Remove a button role from a message by index",
        usage="buttonrole remove <message_link> <index>",
        example="buttonrole remove .../channels/... 2"
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def br_remove(self, ctx, message_link: str = None, index: int = None):
        if not all([message_link, index]):
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        try:
            # Get existing button roles for this message (would need database)
            existing_roles = []  # This would come from database
            
            if not existing_roles:
                await embeds.deny(ctx, "No button roles found for this message.")
                return
            
            if index < 1 or index > len(existing_roles):
                await embeds.deny(ctx, f"Invalid index. Must be between 1 and {len(existing_roles)}.")
                return
            
            # Remove role at index (1-based)
            removed_role = existing_roles.pop(index - 1)
            
            # Create new view with updated buttons
            if existing_roles:
                view = ButtonRoleView(existing_roles)
            else:
                view = None
            
            # Edit message with new view
            await message.edit(view=view)
            
            # Remove from database (would need database implementation)
            # await self.remove_button_role_from_db(ctx.guild.id, channel.id, message.id, removed_role['role_id'])
            
            await embeds.success(ctx, f"Removed button role at index {index}.")
            
        except discord.HTTPException as e:
            await embeds.deny(ctx, f"Failed to edit message: {str(e)}")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @buttonrole.command(
        name="removeall",
        description="Remove all button roles from a message",
        usage="buttonrole removeall <message_link>",
        example="buttonrole removeall .../channels/...",
        aliases=['deleteall', 'delall']
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def br_removeall(self, ctx, message_link: str = None):
        if not message_link:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        try:
            # Remove all buttons from message
            await message.edit(view=None)
            
            # Remove all from database (would need database implementation)
            # await self.remove_all_button_roles_from_db(ctx.guild.id, channel.id, message.id)
            
            await embeds.success(ctx, "Removed all button roles from the message.")
            
        except discord.HTTPException as e:
            await embeds.deny(ctx, f"Failed to edit message: {str(e)}")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @buttonrole.command(
        name="list",
        description="List all button roles in the server",
        usage="buttonrole list",
        example="buttonrole list"
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def br_list(self, ctx):
        button_roles = db.guilds.get_all_button_roles(ctx.guild.id)

        if not button_roles:
            await embeds.default(ctx, "No button roles found in this server.")
            return

        description_lines = []
        for i, br in enumerate(button_roles[:10], 1):  # Limit to 10 for display
            channel = self.bot.get_channel(br['channel_id'])
            role = ctx.guild.get_role(br['role_id'])

            button_display = []
            if br['button_emoji']:
                button_display.append(br['button_emoji'])
            if br['button_label']:
                button_display.append(br['button_label'])

            button_text = " ".join(button_display) if button_display else "Button"

            if channel and role:
                description_lines.append(f"`{i}` {button_text} → {role.mention} in {channel.mention}")
            else:
                description_lines.append(f"`{i}` {button_text} → *Deleted role/channel*")

        if len(button_roles) > 10:
            description_lines.append(f"\n*And {len(button_roles) - 10} more...*")

        await embeds.default(ctx, f"**Button Roles ({len(button_roles)} total):**\n" + "\n".join(description_lines))

    async def parse_message_link(self, ctx, message_link):
        """Parse Discord message link and return message object"""
        if not message_link.startswith('https://discord.com/channels/'):
            await embeds.deny(ctx, "Invalid message link format.")
            return None, None

        try:
            # Extract IDs from message link
            parts = message_link.split('/')
            if len(parts) < 7:
                await embeds.deny(ctx, "Invalid message link format.")
                return None, None

            guild_id = int(parts[4])
            channel_id = int(parts[5])
            message_id = int(parts[6])

            # Check if it's the same guild
            if guild_id != ctx.guild.id:
                await embeds.deny(ctx, "Message must be from this server.")
                return None, None

            # Get channel
            channel = self.bot.get_channel(channel_id)
            if not channel:
                await embeds.deny(ctx, "Channel not found.")
                return None, None

            # Get message
            message = await channel.fetch_message(message_id)
            return message, channel

        except (ValueError, IndexError):
            await embeds.deny(ctx, "Invalid message link format.")
            return None, None
        except discord.NotFound:
            await embeds.deny(ctx, "Message not found.")
            return None, None
        except discord.Forbidden:
            await embeds.deny(ctx, "I don't have permission to access that message.")
            return None, None

async def setup(bot):
    await bot.add_cog(ButtonRole(bot))
