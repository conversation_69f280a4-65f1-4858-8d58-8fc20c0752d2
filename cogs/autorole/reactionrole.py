import discord
from discord.ext import commands
import sys
import os
import re
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from utils.permissions import PermissionCheck, PermissionErrorHandler
from embeds.prebuilt import embeds
from utils.finder import FindRole

class ReactionRole(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    async def cog_command_error(self, ctx, error):
        if isinstance(error, commands.CheckFailure):
            await Permission<PERSON>rror<PERSON>andler(ctx, "permission")

    @commands.group(
        name="reactionrole",
        description="Set up self-assignable roles with reactions",
        usage="reactionrole <subcommand> <args>",
        example="reactionrole add .../channels/... 🌟 @role",
        aliases=['rr']
    )
    @PermissionCheck.discord_permission("ManageGuild")
    async def reactionrole(self, ctx):
        if ctx.invoked_subcommand is None:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_group_help(ctx, ctx.command)

    @reactionrole.command(
        name="add",
        description="Add a reaction role to a message",
        usage="reactionrole add <message_link> <emoji> <role>",
        example="reactionrole add .../channels/... 🌟 @role"
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_add(self, ctx, message_link: str = None, emoji: str = None, *, role_input: str = None):
        if not all([message_link, emoji, role_input]):
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        # Find role
        role = await FindRole(ctx, role_input)
        if not role:
            await embeds.deny(ctx, f"Could not find role: {role_input}")
            return

        # Check role hierarchy
        if role >= ctx.author.top_role and not await self.bot.is_owner(ctx.author):
            await embeds.deny(ctx, "You cannot assign a role higher than or equal to your highest role.")
            return

        if role >= ctx.guild.me.top_role:
            await embeds.deny(ctx, "I cannot assign a role higher than or equal to my highest role.")
            return

        try:
            # Add reaction to message
            await message.add_reaction(emoji)
            
            # Store in database (would need database implementation)
            # await self.add_reaction_role_to_db(ctx.guild.id, channel.id, message.id, emoji, role.id)
            
            await embeds.success(ctx, f"Added reaction role: {emoji} → {role.mention}")
            
        except discord.HTTPException:
            await embeds.deny(ctx, "Failed to add reaction. Invalid emoji or insufficient permissions.")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @reactionrole.command(
        name="remove",
        description="Remove a specific reaction role from a message",
        usage="reactionrole remove <message_link> <emoji>",
        example="reactionrole remove .../channels/... 🌟",
        aliases=['delete', 'del']
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_remove(self, ctx, message_link: str = None, emoji: str = None):
        if not all([message_link, emoji]):
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        try:
            # Remove reaction from message
            await message.clear_reaction(emoji)
            
            # Remove from database (would need database implementation)
            # await self.remove_reaction_role_from_db(ctx.guild.id, channel.id, message.id, emoji)
            
            await embeds.success(ctx, f"Removed reaction role: {emoji}")
            
        except discord.HTTPException:
            await embeds.deny(ctx, "Failed to remove reaction. Invalid emoji or insufficient permissions.")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @reactionrole.command(
        name="removeall",
        description="Remove all reaction roles from a message",
        usage="reactionrole removeall <message_link>",
        example="reactionrole removeall .../channels/...",
        aliases=['deleteall', 'delall']
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_removeall(self, ctx, message_link: str = None):
        if not message_link:
            help_cog = self.bot.get_cog('Help')
            if help_cog:
                await help_cog.show_command_help(ctx, ctx.command)
            return

        # Parse message link
        message, channel = await self.parse_message_link(ctx, message_link)
        if not message:
            return

        try:
            # Clear all reactions from message
            await message.clear_reactions()
            
            # Remove all from database (would need database implementation)
            # await self.remove_all_reaction_roles_from_db(ctx.guild.id, channel.id, message.id)
            
            await embeds.success(ctx, "Removed all reaction roles from the message.")
            
        except discord.HTTPException:
            await embeds.deny(ctx, "Failed to clear reactions. Insufficient permissions.")
        except Exception as e:
            await embeds.deny(ctx, f"An error occurred: {str(e)}")

    @reactionrole.command(
        name="list",
        description="List all reaction roles in the server",
        usage="reactionrole list",
        example="reactionrole list"
    )
    @PermissionCheck.discord_permission("ManageRoles")
    async def rr_list(self, ctx):
        # This would need database implementation
        await embeds.default(ctx, "Reaction role list feature coming soon!")

    @reactionrole.command(
        name="reset",
        description="Remove all reaction roles from the server",
        usage="reactionrole reset",
        example="reactionrole reset"
    )
    @PermissionCheck.discord_permission("ManageGuild")
    async def rr_reset(self, ctx):
        # This would need database implementation
        await embeds.default(ctx, "Reaction role reset feature coming soon!")

    async def parse_message_link(self, ctx, message_link):
        """Parse Discord message link and return message object"""
        if not message_link.startswith('https://discord.com/channels/'):
            await embeds.deny(ctx, "Invalid message link format.")
            return None, None

        try:
            # Extract IDs from message link
            parts = message_link.split('/')
            if len(parts) < 7:
                await embeds.deny(ctx, "Invalid message link format.")
                return None, None

            guild_id = int(parts[4])
            channel_id = int(parts[5])
            message_id = int(parts[6])

            # Check if it's the same guild
            if guild_id != ctx.guild.id:
                await embeds.deny(ctx, "Message must be from this server.")
                return None, None

            # Get channel
            channel = self.bot.get_channel(channel_id)
            if not channel:
                await embeds.deny(ctx, "Channel not found.")
                return None, None

            # Get message
            message = await channel.fetch_message(message_id)
            return message, channel

        except (ValueError, IndexError):
            await embeds.deny(ctx, "Invalid message link format.")
            return None, None
        except discord.NotFound:
            await embeds.deny(ctx, "Message not found.")
            return None, None
        except discord.Forbidden:
            await embeds.deny(ctx, "I don't have permission to access that message.")
            return None, None

async def setup(bot):
    await bot.add_cog(ReactionRole(bot))
