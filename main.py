import discord
from discord.ext import commands
import os
import asyncio
from pathlib import Path
from dotenv import load_dotenv
from database.database import db

load_dotenv()

TOKEN = os.getenv('DISCORD_TOKEN')
PREFIX = os.getenv('DISCORD_PREFIX', ',')

intents = discord.Intents.all()
bot = commands.Bot(command_prefix=PREFIX, intents=intents, help_command=None)

@bot.event
async def on_ready():
    print(f'Bot ready! Logged in as {bot.user} (ID: {bot.user.id})')
    print(f'Connected to {len(bot.guilds)} guilds')
    print(f'Prefix: {PREFIX}')

    # Start cache cleanup task
    bot.loop.create_task(cache_cleanup_task())

@bot.command()
async def ping(ctx):
    await ctx.send(f'Pong! `{round(bot.latency * 1000)}ms`')

@bot.command()
async def cache_stats(ctx):
    if not await bot.is_owner(ctx.author):
        return

    stats = db.get_cache_stats()
    await ctx.send(f"Cache Stats: {stats}")

async def cache_cleanup_task():
    while True:
        await asyncio.sleep(1800)  # 30 minutes
        await db.cleanup_cache()
        print("Cache cleanup completed (Redis handles TTL automatically)")

async def load_cogs():
    # Load cogs
    cogs_path = Path('./cogs')
    if cogs_path.exists():
        for file in cogs_path.rglob('*.py'):
            if file.name.startswith('_'):
                continue

            relative_path = file.relative_to(cogs_path)
            cog_name = f'cogs.{str(relative_path.with_suffix("")).replace("/", ".")}'
            try:
                await bot.load_extension(cog_name)
                print(f'Loaded: {cog_name}')
            except Exception as e:
                print(f'Failed to load {cog_name}: {e}')

    # Load events
    events_path = Path('./events')
    if events_path.exists():
        for file in events_path.rglob('*.py'):
            if file.name.startswith('_'):
                continue

            relative_path = file.relative_to(events_path)
            event_name = f'events.{str(relative_path.with_suffix("")).replace("/", ".")}'
            try:
                await bot.load_extension(event_name)
                print(f'Loaded: {event_name}')
            except Exception as e:
                print(f'Failed to load {event_name}: {e}')

async def load_extensions():
    try:
        await bot.load_extension('jishaku')
        print('Loaded: jishaku')
    except Exception as e:
        print(f'Failed to load jishaku: {e}')

    await load_cogs()

async def main():
    async with bot:
        await db.connect()
        await load_extensions()
        await bot.start(TOKEN)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Bot stopped")
    except Exception as e:
        print(f"Failed to start: {e}")
