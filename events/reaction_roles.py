import discord
from discord.ext import commands
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from database.database import db

class ReactionRoleEvents(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        """Handle reaction role assignment"""
        if payload.user_id == self.bot.user.id:
            return

        # Get reaction role data from database
        reaction_role = await self.get_reaction_role(
            payload.guild_id,
            payload.channel_id,
            payload.message_id,
            str(payload.emoji)
        )

        if not reaction_role:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        role = guild.get_role(reaction_role['role_id'])
        if not role:
            return

        try:
            await member.add_roles(role, reason="Reaction role assignment")
        except discord.Forbidden:
            pass
        except discord.HTTPException:
            pass

    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        """Handle reaction role removal"""
        if payload.user_id == self.bot.user.id:
            return

        # Get reaction role data from database
        reaction_role = await self.get_reaction_role(
            payload.guild_id,
            payload.channel_id,
            payload.message_id,
            str(payload.emoji)
        )

        if not reaction_role:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        role = guild.get_role(reaction_role['role_id'])
        if not role:
            return

        try:
            await member.remove_roles(role, reason="Reaction role removal")
        except discord.Forbidden:
            pass
        except discord.HTTPException:
            pass

    async def get_reaction_role(self, guild_id, channel_id, message_id, emoji):
        """Get reaction role from database"""
        # This would need to be implemented in the database
        # For now, return None
        return None

async def setup(bot):
    await bot.add_cog(ReactionRoleEvents(bot))
