import discord
from discord.ext import commands
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from database.database import db
from utils.permissions import PermissionErrorHandler

class ReactionRoleEvents(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    @commands.Cog.listener()
    async def on_raw_reaction_add(self, payload):
        """Handle reaction role assignment"""
        # Ignore bot reactions
        if payload.user_id == self.bot.user.id:
            return

        # Get reaction role data from database
        reaction_role = await self.get_reaction_role(
            payload.guild_id,
            payload.channel_id,
            payload.message_id,
            str(payload.emoji)
        )

        if not reaction_role:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        role = guild.get_role(reaction_role['role_id'])
        if not role:
            # Role was deleted, should clean up from database
            await self.cleanup_deleted_role(reaction_role['role_id'])
            return

        # Check if user already has the role
        if role in member.roles:
            # User already has the role, no need to add it again
            return

        # Check role hierarchy
        if role >= guild.me.top_role:
            # Bot can't assign this role (role is higher than bot's highest role)
            return

        try:
            await member.add_roles(role, reason="Reaction role assignment")
        except discord.Forbidden:
            # Bot doesn't have permission to manage roles or this specific role
            pass
        except discord.HTTPException:
            # Other Discord API error (rate limit, etc.)
            pass

    @commands.Cog.listener()
    async def on_raw_reaction_remove(self, payload):
        """Handle reaction role removal"""
        if payload.user_id == self.bot.user.id:
            return

        # Get reaction role data from database
        reaction_role = await self.get_reaction_role(
            payload.guild_id,
            payload.channel_id,
            payload.message_id,
            str(payload.emoji)
        )

        if not reaction_role:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        member = guild.get_member(payload.user_id)
        if not member:
            return

        role = guild.get_role(reaction_role['role_id'])
        if not role:
            return

        try:
            await member.remove_roles(role, reason="Reaction role removal")
        except discord.Forbidden:
            pass
        except discord.HTTPException:
            pass

    async def get_reaction_role(self, guild_id, channel_id, message_id, emoji):
        """Get reaction role from database"""
        return db.guild_model.get_reaction_role(guild_id, channel_id, message_id, emoji)

    async def cleanup_deleted_role(self, role_id):
        """Clean up deleted role from database"""
        return db.guild_model.cleanup_deleted_role(role_id)

async def setup(bot):
    await bot.add_cog(ReactionRoleEvents(bot))
