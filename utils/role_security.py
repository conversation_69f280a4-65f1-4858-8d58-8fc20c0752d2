import discord
from typing import List

class RoleSecurity:
    """Security checks for role assignments"""
    
    # Dangerous permissions that shouldn't be assignable via reaction/button roles
    DANGEROUS_PERMISSIONS = [
        'administrator',
        'manage_guild',
        'manage_roles',
        'manage_channels',
        'manage_messages',
        'manage_webhooks',
        'manage_emojis_and_stickers',
        'kick_members',
        'ban_members',
        'moderate_members',  # timeout/mute
        'manage_nicknames',
        'mute_members',
        'deafen_members',
        'move_members',
        'mention_everyone',
        'view_audit_log',
        'manage_events',
        'manage_threads'
    ]
    
    @classmethod
    def has_dangerous_permissions(cls, role: discord.Role) -> tuple[bool, List[str]]:
        """
        Check if a role has dangerous permissions
        Returns: (has_dangerous, list_of_dangerous_perms)
        """
        dangerous_perms = []
        
        for perm_name in cls.DANGEROUS_PERMISSIONS:
            if hasattr(role.permissions, perm_name):
                if getattr(role.permissions, perm_name):
                    dangerous_perms.append(perm_name.replace('_', ' ').title())
        
        return len(dangerous_perms) > 0, dangerous_perms
    
    @classmethod
    def can_user_assign_role(cls, user: discord.Member, role: discord.Role, bot_member: discord.Member) -> tuple[bool, str]:
        """
        Check if user can assign a specific role
        Returns: (can_assign, error_reason)
        """
        # Check if role has dangerous permissions
        has_dangerous, dangerous_perms = cls.has_dangerous_permissions(role)
        if has_dangerous:
            return False, f"Role has dangerous permissions: {', '.join(dangerous_perms[:3])}{'...' if len(dangerous_perms) > 3 else ''}"
        
        # Check user hierarchy (user must be higher than the role)
        if role >= user.top_role:
            return False, f"You cannot assign a role ({role.name}) that is higher than or equal to your highest role ({user.top_role.name})"
        
        # Check bot hierarchy (bot must be higher than the role)
        if role >= bot_member.top_role:
            return False, f"Bot cannot assign a role ({role.name}) that is higher than or equal to bot's highest role ({bot_member.top_role.name})"
        
        # Check if role is @everyone
        if role.is_default():
            return False, "Cannot assign the @everyone role"
        
        # Check if role is managed (bot roles, booster roles, etc.)
        if role.managed:
            return False, f"Cannot assign managed role ({role.name}) - this role is managed by Discord or an integration"
        
        return True, ""
    
    @classmethod
    def format_dangerous_permissions_list(cls, dangerous_perms: List[str]) -> str:
        """Format dangerous permissions for user display"""
        if len(dangerous_perms) <= 3:
            return ", ".join(dangerous_perms)
        else:
            return f"{', '.join(dangerous_perms[:3])} and {len(dangerous_perms) - 3} more"
