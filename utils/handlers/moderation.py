import discord
from discord.ext import commands
from utils.permissions import CanModerate, CheckBotHierarchy, PermissionErrorHandler

async def CheckModerationPermissions(ctx: commands.Context, target: discord.Member) -> bool:
    """Check if user can moderate target and bot has permissions"""

    # Check if user can moderate target
    if not CanModerate(ctx.author, target):
        await PermissionErrorHandler(ctx, "hierarchy")
        return False

    # Check if bot can moderate target
    if not await CheckBotHierarchy(ctx, target):
        return False

    return True

async def HandleTimeout(member: discord.Member, duration: int, reason: str = "No reason provided"):
    """Handle member timeout"""
    from datetime import datetime, timedelta
    timeout_until = datetime.now() + timedelta(seconds=duration)
    await member.timeout(timeout_until, reason=reason)

async def HandleRemoveTimeout(member: discord.Member, reason: str = "No reason provided"):
    """Handle removing member timeout"""
    await member.timeout(None, reason=reason)

async def HandleKick(member: discord.Member, reason: str = "No reason provided"):
    """Handle member kick"""
    await member.kick(reason=reason)

async def HandleBan(guild: discord.Guild, user: discord.abc.User, reason: str = "No reason provided"):
    """Handle user ban (works for both members and users)"""
    await guild.ban(user, reason=reason)

async def HandleUnban(guild: discord.Guild, user_id: int, reason: str = "No reason provided"):
    """Handle member unban"""
    user = await guild.fetch_ban(discord.Object(id=user_id))
    await guild.unban(user.user, reason=reason)
