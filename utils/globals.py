"""
Global imports and utilities for the bot
This module provides commonly used imports and utilities across the entire bot

Usage in any cog:
    from utils.globals import *

This gives you access to all commonly used imports without needing individual imports.
"""

# Core Discord imports
import discord
from discord.ext import commands

# Standard library commonly used
import sys
import os
import re
from typing import Optional, List, Dict, Union
from datetime import datetime, timedelta

# Bot utilities
from utils.config import config, colors, emotes
from utils.finder import FindUser, FindRole, FindChannel
from utils.permissions import (
    PermissionCheck,
    PermissionErrorHandler,
    ParseMessageLink,
    HandleMessageLinkError,
    CanModerate,
    HasDiscordPermission,
    IsBotOwner
)
from utils.error_tracker import error_tracker, handle_command_error

# Embed systems
from embeds.prebuilt import embeds
from embeds.parse import create_embed
from embeds.button import create_pagination_view, create_confirm_view

# Database
from database.database import db

# Security
from utils.role_security import RoleSecurity
