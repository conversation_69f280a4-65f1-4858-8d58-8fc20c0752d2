"""
Global imports and utilities for the bot
This module provides commonly used imports and utilities across the entire bot
"""

# Core Discord imports
import discord
from discord.ext import commands

# Bot utilities
from utils.config import config, colors, emotes
from utils.finder import FindUser, FindRole, FindChannel
from utils.permissions import (
    PermissionCheck, 
    PermissionErrorHandler, 
    ParseMessageLink, 
    HandleMessageLinkError,
    CanModerate,
    HasDiscordPermission,
    IsBotOwner
)
from utils.error_tracker import error_tracker, handle_command_error

# Embed systems
from embeds.prebuilt import embeds
from embeds.parse import create_embed
from embeds.button import create_pagination_view, create_confirm_view

# Database
from database.database import db

# Security
from utils.role_security import RoleSecurity

# Standard library commonly used
import sys
import os
import re
from typing import Optional, List, Dict, Union
from datetime import datetime, timedelta

# Export all commonly used items
__all__ = [
    # Discord
    'discord', 'commands',
    
    # Config
    'config', 'colors', 'emotes',
    
    # Finders
    'FindUser', 'FindRole', 'FindChannel',
    
    # Permissions
    'PermissionCheck', 'PermissionErrorHandler', 'ParseMessageLink', 
    'HandleMessageLinkError', 'CanModerate', 'HasDiscordPermission', 'IsBotOwner',
    
    # Error tracking
    'error_tracker', 'handle_command_error',
    
    # Embeds
    'embeds', 'create_embed', 'create_pagination_view', 'create_confirm_view',
    
    # Database
    'db',
    
    # Security
    'RoleSecurity',
    
    # Standard library
    'sys', 'os', 're', 'Optional', 'List', 'Dict', 'Union', 'datetime', 'timedelta'
]
