import random
import string
import traceback
from datetime import datetime
from typing import Dict, Optional
import discord
from discord.ext import commands

class ErrorTracker:
    """Global error tracking system for the bot"""
    
    def __init__(self):
        self.errors: Dict[str, dict] = {}
    
    def generate_error_code(self) -> str:
        """Generate a unique error code"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    
    def log_error(self, ctx: commands.Context, error: Exception, command_name: str = None) -> str:
        """Log an error and return the error code"""
        error_code = self.generate_error_code()
        
        # Get command name
        if not command_name:
            command_name = ctx.command.qualified_name if ctx.command else "Unknown"
        
        # Get full traceback
        error_traceback = ''.join(traceback.format_exception(type(error), error, error.__traceback__))
        
        # Store error details
        self.errors[error_code] = {
            'timestamp': datetime.now(),
            'user_id': ctx.author.id,
            'user_name': ctx.author.name,
            'guild_id': ctx.guild.id if ctx.guild else None,
            'guild_name': ctx.guild.name if ctx.guild else "DM",
            'channel_id': ctx.channel.id,
            'channel_name': getattr(ctx.channel, 'name', 'DM'),
            'command': command_name,
            'message_content': ctx.message.content,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': error_traceback
        }
        
        return error_code
    
    def get_error(self, error_code: str) -> Optional[dict]:
        """Get error details by code"""
        return self.errors.get(error_code)
    
    def clear_old_errors(self, days: int = 7):
        """Clear errors older than specified days"""
        cutoff = datetime.now().timestamp() - (days * 24 * 60 * 60)
        to_remove = []
        
        for code, error_data in self.errors.items():
            if error_data['timestamp'].timestamp() < cutoff:
                to_remove.append(code)
        
        for code in to_remove:
            del self.errors[code]

# Global error tracker instance
error_tracker = ErrorTracker()

async def handle_command_error(ctx: commands.Context, error: Exception, command_name: str = None) -> str:
    """Handle command errors and return error code"""
    from embeds.prebuilt import embeds
    from utils.config import config
    
    # Log the error and get code
    error_code = error_tracker.log_error(ctx, error, command_name)
    
    # Send user-friendly error message
    await embeds.warn(
        ctx, 
        f"{ctx.author.mention}: Error occurred while performing command **{command_name or 'unknown'}**. "
        f"Please report the error code to the **developer in [official server]({config.support_server})**"
    )
    
    # Send error code outside embed
    await ctx.send(f"`{error_code}`")
    
    return error_code
