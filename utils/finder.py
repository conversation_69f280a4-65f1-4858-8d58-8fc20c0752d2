import discord
from discord.ext import commands
from typing import Optional
import re

async def FindMember(ctx: commands.Context, user_input: str = None) -> Optional[discord.Member]:
    """Find a member within the current server only"""
    if not user_input:
        return ctx.author

    user_input = user_input.strip()

    # Try ID (mention or raw ID)
    user_id_match = re.search(r'(\d{15,21})', user_input)
    if user_id_match:
        user_id = int(user_id_match.group(1))
        member = ctx.guild.get_member(user_id)
        if member:
            return member

    # Try username (case insensitive)
    for member in ctx.guild.members:
        if member.name.lower() == user_input.lower():
            return member

    # Try display name (case insensitive)
    for member in ctx.guild.members:
        if member.display_name.lower() == user_input.lower():
            return member

    # Try partial username match
    for member in ctx.guild.members:
        if user_input.lower() in member.name.lower():
            return member

    # Try partial display name match
    for member in ctx.guild.members:
        if user_input.lower() in member.display_name.lower():
            return member

    return None

async def FindUser(ctx: commands.Context, user_input: str = None) -> Optional[discord.User]:
    """Find a user globally (can be outside the server)"""
    if not user_input:
        return ctx.author

    user_input = user_input.strip()

    # Try ID (mention or raw ID) - this works globally
    user_id_match = re.search(r'(\d{15,21})', user_input)
    if user_id_match:
        user_id = int(user_id_match.group(1))

        # First try to get as member (if in server)
        member = ctx.guild.get_member(user_id)
        if member:
            return member

        # Then try to fetch as user globally
        try:
            user = await ctx.bot.fetch_user(user_id)
            if user:
                return user
        except (discord.NotFound, discord.HTTPException):
            pass

    # For non-ID searches, first check server members
    member = await FindMember(ctx, user_input)
    if member:
        return member

    # For global search by username, we'd need to search bot's cache
    # This is limited but better than nothing
    for user in ctx.bot.users:
        if user.name.lower() == user_input.lower():
            return user

    # Try partial match in bot's user cache
    for user in ctx.bot.users:
        if user_input.lower() in user.name.lower():
            return user

    return None

async def FindUserOrMember(ctx: commands.Context, user_input: str = None, server_only: bool = False):
    """Smart user finder that returns appropriate type based on context"""
    if server_only:
        return await FindMember(ctx, user_input)
    else:
        return await FindUser(ctx, user_input)

async def FindRole(ctx: commands.Context, role_input: str) -> Optional[discord.Role]:
    if not role_input:
        return None
    
    role_input = role_input.strip()
    
    # Try ID (mention or raw ID)
    role_id_match = re.search(r'(\d{15,21})', role_input)
    if role_id_match:
        role_id = int(role_id_match.group(1))
        role = ctx.guild.get_role(role_id)
        if role:
            return role
    
    # Try exact name match (case insensitive)
    for role in ctx.guild.roles:
        if role.name.lower() == role_input.lower():
            return role
    
    # Try partial name match
    for role in ctx.guild.roles:
        if role_input.lower() in role.name.lower():
            return role
    
    return None

async def FindChannel(ctx: commands.Context, channel_input: str) -> Optional[discord.TextChannel]:
    if not channel_input:
        return None
    
    channel_input = channel_input.strip()
    
    # Try ID (mention or raw ID)
    channel_id_match = re.search(r'(\d{15,21})', channel_input)
    if channel_id_match:
        channel_id = int(channel_id_match.group(1))
        channel = ctx.guild.get_channel(channel_id)
        if isinstance(channel, discord.TextChannel):
            return channel
    
    # Try exact name match (case insensitive)
    for channel in ctx.guild.text_channels:
        if channel.name.lower() == channel_input.lower():
            return channel
    
    # Try partial name match
    for channel in ctx.guild.text_channels:
        if channel_input.lower() in channel.name.lower():
            return channel
    
    return None


async def FindCategory(ctx: commands.Context, category_input: str) -> Optional[discord.CategoryChannel]:
    if not category_input:
        return None
    
    category_input = category_input.strip()
    
    # Try ID (raw ID)
    category_id_match = re.search(r'(\d{15,21})', category_input)
    if category_id_match:
        category_id = int(category_id_match.group(1))
        category = ctx.guild.get_channel(category_id)
        if isinstance(category, discord.CategoryChannel):
            return category
    
    # Try exact name match (case insensitive)
    for category in ctx.guild.categories:
        if category.name.lower() == category_input.lower():
            return category
    
    # Try partial name match
    for category in ctx.guild.categories:
        if category_input.lower() in category.name.lower():
            return category
    
    return None


