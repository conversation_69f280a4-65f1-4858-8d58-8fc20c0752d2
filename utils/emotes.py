class Emotes:
    # Core emotes
    cooldown = '<:cooldown:1379296257746538677>'
    success = '<:success:1379289220606070834>'
    error = '<:error:1379296270438371419>'
    warn = '<:warn:1379296346334433316>'
    left = '<:left:1380275840004132864>'
    right = '<:right:1379296316081045595>'
    cancel = '<:cancel:1379296264008634486>'
    choose = '<:choose:1379296334321942690>'
    loading = '<a:loading:1379296276356665364>'
    lastfm = '<:lastfm:1379296282690195496>'
    join = '<:success:1379289220606070834>'
    leave = '<:error:1379296270438371419>'
    cross = '<:x:1382101646598738111>'
    circle = '<:o:1382101649732014080>'
    ticket = '<:Ticket:1385950797895958618>'

    # Voice Manager emotes
    vmLock = '<:lock:1386222503999049838>'
    vmUnlock = '<:unlock:1386222513490624512>'
    vmHide = '<:hide:1386222497283833876>'
    vmUnhide = '<:unhide:1386222511167115334>'
    vmRename = '<:rename:1386222506322563073>'
    vmClaim = '<:crown:1386222492280029305>'
    vmIncrease = '<:increase:1386222499544563826>'
    vmDecrease = '<:decrease:1386222494729371709>'
    vmDelete = '<:trash:1386222508948062310>'
    vmInfo = '<:infomation:1386222501498978456>'

    # Level Progress Bar emotes
    blueLeftRounded = '<:blue_left_rounded:1386722842002985082>'
    blue = '<:blue:1386722849007468544>'
    blueRightRounded = '<:blue_right_rounded:1386722855038750892>'
    whiteLeftRounded = '<:white_left_rounded:1386722861833650388>'
    white = '<:white:1386722868062060598>'
    whiteRightRounded = '<:white_right_rounded:1386722874554847293>'

    # Other emotes
    google = '<:Google:1385950785975746684>'

# Global instance for easy importing
emotes = Emotes()
