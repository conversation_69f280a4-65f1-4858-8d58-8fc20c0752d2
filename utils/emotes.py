class Emotes:
    cancel = '<:cancel:1388159577513791558>'
    choose = '<:choose:1388159583310188756>'
    error = '<:error:1388159552884838563>'
    left = '<:left:1388159565350047905>'
    right = '<:right:1388159571708739694>'
    success = '<:success:1388159547113345207>'
    warn = '<:warn:1388159559419432960>'

class Colors:
    cooldown = 0xa5eb78
    success = 0xa4ec7c
    error = 0xfc6464
    warn = 0xfbd03b
    default = 0x3a3a40

class Config:
    support_server = "https://discord.gg/ge8gxgVE9U"

emotes = Emotes()
colors = Colors()
config = Config()
