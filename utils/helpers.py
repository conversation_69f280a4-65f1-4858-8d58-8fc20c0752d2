import discord
from discord.ext import commands
from typing import Optional, Union, List
import os

def format_time(seconds: int) -> str:
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    if hours:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"

def clean_content(content: str, max_length: int = 2000) -> str:
    if len(content) <= max_length:
        return content
    return content[:max_length - 3] + "..."

async def get_member(ctx: commands.Context, user_input: str) -> Optional[discord.Member]:
    if not user_input:
        return ctx.author

    try:
        user_id = int(user_input.strip('<@!>'))
        return ctx.guild.get_member(user_id)
    except ValueError:
        return discord.utils.get(ctx.guild.members, name=user_input)

# Moved to utils/embeds.py
